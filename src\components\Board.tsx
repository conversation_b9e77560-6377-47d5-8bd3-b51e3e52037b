import { BoardProps } from "@/types/components";
import { BoardContext } from "@/context/BoardContext";
import { Children, cloneElement, isValidElement, useCallback, useRef, useState } from "react";

const Board = ({
  id = "",
  className = "",
  children,
  onItemMove,
}: BoardProps) => {
  const [draggingNote, setDraggingNote] = useState<string | number | null>(null);
  const mouseStartPos = useRef({ x: 0, y: 0 });
  const containerRef = useRef(null);

  const updateItemPosition = useCallback((id: string | number, mouseMoveDir: { x: number, y: number }) => {
    onItemMove?.(id, mouseMoveDir);
  }, [onItemMove]);

  const isDragging = useCallback((id: string | number) => {
    return draggingNote === id;
  }, [draggingNote]);

  const contextValue = {
    draggingNote,
    setDraggingNote,
    isDragging,
  }

  return (
    <BoardContext.Provider value={contextValue}>
      <div
        id={id}
        className={className}
        ref={containerRef}
      >
        {children}
      </div>
    </BoardContext.Provider>
  );
};

export default Board;
