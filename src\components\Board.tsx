import { BoardProps } from "@/types/components";
import { BoardContext } from "@/context/BoardContext";
import { useCallback, useRef, useState } from "react";

const Board = ({
  id = "",
  className = "",
  children,
  onItemMove,
}: BoardProps) => {
  const [draggingNote, setDraggingNote] = useState<string | number | null>(null);
  const mouseStartPos = useRef({ x: 0, y: 0 });
  const containerRef = useRef(null);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    console.log('mouse down', e.target);
    mouseStartPos.current = { x: e.clientX, y: e.clientY };
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }

  const handleMouseMove = (e: MouseEvent) => {
    console.log('mouse move');
    if (!draggingNote) return;

    const container = containerRef.current;
    if (!container) return;

    const { x, y } = mouseStartPos.current;
    const { clientX, clientY } = e;
    const dx = clientX - x;
    const dy = clientY - y;

    updateItemPosition(draggingNote, { x: dx, y: dy });

    mouseStartPos.current = { x: clientX, y: clientY };
  }

  const handleMouseUp = () => {
    console.log('mouse up');
    setDraggingNote(null);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }

  const updateItemPosition = useCallback((id: string | number, mouseMoveDir: { x: number, y: number }) => {
    onItemMove?.(id, mouseMoveDir);
  }, [onItemMove]);

  const isDragging = useCallback((id: string | number) => {
    return draggingNote === id;
  }, [draggingNote]);

  const contextValue = {
    onMouseDown: handleMouseDown,
    draggingNote,
    setDraggingNote,
    isDragging,
  }

  return (
    <BoardContext.Provider value={contextValue}>
      <div
        id={id}
        className={className}
        ref={containerRef}
      >
        {children}
      </div>
    </BoardContext.Provider>
  );
};

export default Board;
