import { useState } from "react";
import Board from "./components/Board";
import BoardItem from "./components/BoardItem";

function App() {
  const [notes, setNotes] = useState([
    { noteId: 1, title: "Note 1", content: "Content 1", color: '#FEE5FD', position: { x: 505, y: 10 } },
    { noteId: 2, title: "Note 2", content: "Content 2", color: '#A6DCE9', position: { x: 305, y: 400 } },
    { noteId: 3, title: "Note 3", content: "Content 3", color: '#FFF5DF', position: { x: 800, y: 500 } },
    { noteId: 4, title: "Note 4", content: "Content 4", color: '#E4CCFF', position: { x: 0, y: 0 } },
    { noteId: 5, title: "Note 5", content: "Content 5", color: '#AFF4C6', position: { x: 900, y: 80 } },
  ]);
  
  return (
    <div id="app">
      <Board id="board">
        {notes.map(note => {
          return (
            <BoardItem
              noteId={note.noteId}
              key={note.noteId}
              className="board-item abs sfd-asd"
              style={{
                width: '160px',
                height: '160px',
                backgroundColor: note.color
              }}
              position={note.position}
              config={{
                behavior: 'snap',
                step: 1
              }}
            >
              abc
            </BoardItem>
          )
        })}
      </Board>
    </div>
  );
}

export default App;
