:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  padding: 0;
  margin: 0;
}

#board {
  background-color: #212228;
  background-image: linear-gradient(#292a30 0.1em, transparent 0.1em),
      linear-gradient(90deg, #292a30 0.1em, transparent 0.1em);
  background-size: 4em 4em;
  background-attachment: local;
  height: 100vh;
  position: relative;
  overflow: auto;
}

.board-item {
  user-select: none;
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075), 0 2px 2px hsl(0deg 0% 0% /
                  0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075), 0 8px 8px hsl(0deg
                  0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
}

.board-item.selected {
  opacity: 0.8;
}

.board-item.selected::after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 5px;
  outline: 3px solid #0D99FF;
}

.folder {
  position: absolute;
  width: 80px;
  height: 80px;
  background-color: #dacc08;
  top: 16px;
  right: 16px;
}