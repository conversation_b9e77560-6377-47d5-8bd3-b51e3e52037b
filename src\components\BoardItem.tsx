import React from 'react'
import { setNewOffset, setZIndex } from '../utils/utils'
import { BoardItemProps, DragConfig } from '@/types/components'

const BoardItem = ({
  noteId,
  id="",
  className="",
  style,
  position={
    x: 0,
    y: 0
  },
  onMouseDown,
  config,
  children
}: BoardItemProps) => {
  const [currentPos, setCurrentPos] = React.useState(position);
  const itemRef = React.useRef<HTMLDivElement>(null);
  
  const dragDefaultConfig: DragConfig = {
    behavior: "smooth",
    step: 20,
    borderWidth: 10,
    ...config,
  }
  const { borderWidth = 0 } = dragDefaultConfig;
  
  return (
    <div
      data-note-id={noteId}
      ref={itemRef}
      id={id}
      className={className} 
      style={{
        position: 'absolute',
        left: `${currentPos.x}px`,
        top: `${currentPos.y}px`,
        minWidth: 'fit-content',
        minHeight: 'fit-content',
        ...style
      }}
      onMouseDown={onMouseDown ? (e) => onMouseDown(noteId, e) : undefined}
      //onMouseMove={hoverOnBorder}
    >
      {children}
    </div>
  )
}

export default BoardItem
