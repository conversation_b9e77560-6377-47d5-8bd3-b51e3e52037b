export const setNewOffset = (item: HTMLDivElement | null, mouseMoveDir = { x: 0, y: 0 }) => {
  if (!item) return { x: 0, y: 0 };

  const currentLeft = parseInt(item.style.left) || 0;
  const currentTop = parseInt(item.style.top) || 0;
  const offsetLeft = currentLeft + mouseMoveDir.x;
  const offsetTop = currentTop + mouseMoveDir.y;
  const itemWidth = item.clientWidth || 0;
  const itemHeight = item.clientHeight || 0;

  // Get container bounds (assuming parent container)
  const container = item.parentElement;
  const containerWidth = container?.clientWidth || window.innerWidth;
  const containerHeight = container?.clientHeight || window.innerHeight;

  return {
    x: Math.max(0, Math.min(offsetLeft, containerWidth - itemWidth)),
    y: Math.max(0, Math.min(offsetTop, containerHeight - itemHeight)),
  };
};

export const setZIndex = (selectedItem: HTMLDivElement | null) => {
  if (!selectedItem) return;
  selectedItem.style.zIndex = '999';

  Array.from(document.getElementsByClassName(selectedItem?.className || '')).forEach(item => {
    if (item !== selectedItem && item instanceof HTMLElement) {
      const zIndex = Number(selectedItem.style.zIndex || '0') - 1;

      item.style.zIndex = zIndex.toString();
    }
  });
};