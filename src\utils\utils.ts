export const setNewOffset = (item: HTMLDivElement | null, mouseMoveDir = { x: 0, y: 0 }) => {
  const offsetLeft = (item?.offsetLeft || 0) + mouseMoveDir.x;
  const offsetTop = (item?.offsetTop || 0) + mouseMoveDir.y;
  const itemWidth = (item?.clientWidth || 0);
  const itemHeight = (item?.clientHeight || 0);

  return {
    x: offsetLeft < 0 ? 0 : (offsetLeft > window.innerWidth - itemWidth ? window.innerWidth - itemWidth : offsetLeft),
    y: offsetTop < 0 ? 0 : (offsetTop > window.innerHeight - itemHeight ? window.innerHeight - itemHeight : offsetTop),
  };
};

export const setZIndex = (selectedItem: HTMLDivElement | null) => {
  if (!selectedItem) return;
  selectedItem.style.zIndex = '999';

  Array.from(document.getElementsByClassName(selectedItem?.className || '')).forEach(item => {
    if (item !== selectedItem && item instanceof HTMLElement) {
      const zIndex = Number(selectedItem.style.zIndex || '0') - 1;

      item.style.zIndex = zIndex.toString();
    }
  });
};