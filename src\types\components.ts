export interface BoardProps {
  id?: string;
  className?: string;
  children?: React.ReactNode;
  onItemMove?: (noteId: string | number, position: { x: number; y: number }) => void;
}

export type DragConfig = {
  behavior?: "smooth" | "snap";
  step?: number;
  borderWidth?: number
}

export interface BoardItemProps {
  noteId: string | number;
  id?: string;
  className?: string;
  style?: object,
  position?: {
    x: number;
    y: number;
  },
  onMouseDown?: (noteId: string | number, event: React.MouseEvent) => void;
  config?: DragConfig;
  children?: React.ReactNode;
}